# =============================================================================
# Docker Ignore File for Habit Tracker
# =============================================================================
# This file specifies which files and directories should be excluded
# when building the Docker image.

# =============================================================================
# Version Control
# =============================================================================
.git
.gitignore
.gitattributes

# =============================================================================
# Documentation
# =============================================================================
README.md
*.md
docs/
documentation/

# =============================================================================
# Development Files
# =============================================================================
.env
.env.local
.env.*.local
*.log
logs/

# =============================================================================
# IDE and Editor Files
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# OS Generated Files
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# Python Cache and Virtual Environments
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# =============================================================================
# Testing and Coverage
# =============================================================================
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# =============================================================================
# Terraform Files
# =============================================================================
terraform/
*.tf
*.tfstate
*.tfstate.*
.terraform/

# =============================================================================
# Docker Files (except the one we're using)
# =============================================================================
docker-compose*.yml
Dockerfile*
.dockerignore

# =============================================================================
# CI/CD Files
# =============================================================================
.github/
.gitlab-ci.yml
.travis.yml
azure-pipelines.yml

# =============================================================================
# Temporary Files
# =============================================================================
*.tmp
*.temp
*.bak
*.backup

# =============================================================================
# Application Specific
# =============================================================================
# Keep only the necessary app files
# Exclude test files from the main app directory
habit-tracker/app/tests/
habit-tracker/app/*_test.py
habit-tracker/app/test_*.py

# Exclude development scripts
habit-tracker/run_tests.py
habit-tracker/pytest.ini 