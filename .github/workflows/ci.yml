name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r habit-tracker/requirements.txt

      - name: Lint with flake8
        run: |
          echo "Running flake8..."
          flake8 habit-tracker/app
          echo "Linting completed."

      - name: Run unit tests
        run: |
          echo "Running pytest with SQLite in-memory..."
          cd habit-tracker
          pytest tests -v --cov=app --cov-report=xml
          echo "Unit tests completed."

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./habit-tracker/coverage.xml
          flags: unittests
          name: codecov-umbrella

  docker-build:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          target: production
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  terraform-plan:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"

      - name: Validate Required Secrets
        run: |
          if [ -z "${{ secrets.AZURE_CREDENTIALS }}" ]; then
            echo "❌ AZURE_CREDENTIALS secret is not set"
            exit 1
          fi
          if [ -z "${{ secrets.TF_VAR_FLASK_SECRET_KEY }}" ]; then
            echo "❌ TF_VAR_FLASK_SECRET_KEY secret is not set"
            exit 1
          fi
          echo "✅ Required secrets are configured"

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        working-directory: terraform
        run: terraform init

      - name: Terraform Plan with Import Handling
        working-directory: terraform
        env:
          TF_VAR_flask_secret_key: ${{ secrets.TF_VAR_FLASK_SECRET_KEY }}
          TF_VAR_domain_name: ${{ secrets.TF_VAR_DOMAIN_NAME }}
          TF_VAR_ssl_email: ${{ secrets.TF_VAR_SSL_EMAIL }}
          TF_VAR_github_token: ${{ secrets.GITHUB_TOKEN }}
          TF_VAR_github_username: ${{ secrets.TF_VAR_GITHUB_USERNAME }}
          TF_VAR_container_image_name: ${{ secrets.TF_VAR_CONTAINER_IMAGE_NAME }}
        run: |
          # Try to plan, and if it fails due to existing resources, attempt to import them
          if ! terraform plan -var-file="environments/prod.tfvars" -out=tfplan 2>&1 | tee plan_output.log; then
            echo "Plan failed, checking for existing resources..."

            # Check for resource group import needed
            if grep -q "habit-tracker-prod-rg.*already exists" plan_output.log; then
              echo "Importing existing resource group..."
              terraform import azurerm_resource_group.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg || true
            fi

            # Check for virtual network import needed
            if grep -q "habit-tracker-prod-vnet.*already exists" plan_output.log; then
              echo "Importing existing virtual network..."
              terraform import azurerm_virtual_network.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/virtualNetworks/habit-tracker-prod-vnet || true
            fi

            # Check for public IP import needed
            if grep -q "habit-tracker-prod-pip.*already exists" plan_output.log; then
              echo "Importing existing public IP..."
              terraform import azurerm_public_ip.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/publicIPAddresses/habit-tracker-prod-pip || true
            fi

            # Check for NSG import needed
            if grep -q "habit-tracker-prod-web-nsg.*already exists" plan_output.log; then
              echo "Importing existing network security group..."
              terraform import azurerm_network_security_group.web /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/networkSecurityGroups/habit-tracker-prod-web-nsg || true
            fi

            # Check for private DNS zone import needed
            if grep -q "habit-tracker-prod-postgres.private.postgres.database.azure.com.*already exists" plan_output.log; then
              echo "Importing existing private DNS zone..."
              terraform import azurerm_private_dns_zone.postgres /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/privateDnsZones/habit-tracker-prod-postgres.private.postgres.database.azure.com || true
            fi

            # Retry the plan after imports
            echo "Retrying plan after imports..."
            terraform plan -var-file="environments/prod.tfvars" -out=tfplan
          fi

          terraform show -no-color tfplan > plan.txt

      - name: Comment PR with Terraform Plan
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const fs = require('fs');
            const plan = fs.readFileSync('terraform/plan.txt', 'utf8');
            const maxGitHubBodyCharacters = 65536;

            function chunkSubstr(str, size) {
              const numChunks = Math.ceil(str.length / size)
              const chunks = new Array(numChunks)
              for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
                chunks[i] = str.substr(o, size)
              }
              return chunks
            }

            const planChunks = chunkSubstr(plan, maxGitHubBodyCharacters);

            for (let i = 0; i < planChunks.length; i++) {
              const output = `### Terraform Plan Part ${i + 1}

            \`\`\`terraform
            ${planChunks[i]}
            \`\`\`

            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`, Working Directory: \`terraform\`, Workflow: \`${{ github.workflow }}\`*`;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: output
              });
            }

      - name: Save Terraform Plan Summary
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
        run: |
          echo "## Terraform Plan Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment**: Production" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Plan Output" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          head -50 terraform/plan.txt >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "✅ Plan completed successfully. Ready for apply." >> $GITHUB_STEP_SUMMARY

  terraform-apply:
    needs: [docker-build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    environment: production

    outputs:
      vm_ip: ${{ steps.terraform-output.outputs.vm_ip }}
      ssh_private_key: ${{ steps.terraform-output.outputs.ssh_private_key }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Validate Required Secrets
        run: |
          if [ -z "${{ secrets.AZURE_CREDENTIALS }}" ]; then
            echo "❌ AZURE_CREDENTIALS secret is not set"
            exit 1
          fi
          if [ -z "${{ secrets.TF_VAR_FLASK_SECRET_KEY }}" ]; then
            echo "❌ TF_VAR_FLASK_SECRET_KEY secret is not set"
            exit 1
          fi
          echo "✅ Required secrets are configured"

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Terraform Init
        working-directory: terraform
        run: terraform init

      - name: Terraform Apply with Import Handling
        working-directory: terraform
        env:
          TF_VAR_flask_secret_key: ${{ secrets.TF_VAR_FLASK_SECRET_KEY }}
          TF_VAR_domain_name: ${{ secrets.TF_VAR_DOMAIN_NAME }}
          TF_VAR_ssl_email: ${{ secrets.TF_VAR_SSL_EMAIL }}
          TF_VAR_github_token: ${{ secrets.GITHUB_TOKEN }}
          TF_VAR_github_username: ${{ secrets.TF_VAR_GITHUB_USERNAME }}
          TF_VAR_container_image_name: ${{ secrets.TF_VAR_CONTAINER_IMAGE_NAME }}
        run: |
          # Try to apply, and if it fails due to existing resources, attempt to import them
          if ! terraform apply -var-file="environments/prod.tfvars" -auto-approve 2>&1 | tee apply_output.log; then
            echo "Apply failed, checking for existing resources..."

            # Check for resource group import needed
            if grep -q "habit-tracker-prod-rg.*already exists" apply_output.log; then
              echo "Importing existing resource group..."
              terraform import azurerm_resource_group.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg || true
            fi

            # Check for virtual network import needed
            if grep -q "habit-tracker-prod-vnet.*already exists" apply_output.log; then
              echo "Importing existing virtual network..."
              terraform import azurerm_virtual_network.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/virtualNetworks/habit-tracker-prod-vnet || true
            fi

            # Check for public IP import needed
            if grep -q "habit-tracker-prod-pip.*already exists" apply_output.log; then
              echo "Importing existing public IP..."
              terraform import azurerm_public_ip.main /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/publicIPAddresses/habit-tracker-prod-pip || true
            fi

            # Check for NSG import needed
            if grep -q "habit-tracker-prod-web-nsg.*already exists" apply_output.log; then
              echo "Importing existing network security group..."
              terraform import azurerm_network_security_group.web /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/networkSecurityGroups/habit-tracker-prod-web-nsg || true
            fi

            # Check for private DNS zone import needed
            if grep -q "habit-tracker-prod-postgres.private.postgres.database.azure.com.*already exists" apply_output.log; then
              echo "Importing existing private DNS zone..."
              terraform import azurerm_private_dns_zone.postgres /subscriptions/$(az account show --query id -o tsv)/resourceGroups/habit-tracker-prod-rg/providers/Microsoft.Network/privateDnsZones/habit-tracker-prod-postgres.private.postgres.database.azure.com || true
            fi

            # Retry the apply after imports
            echo "Retrying apply after imports..."
            terraform apply -var-file="environments/prod.tfvars" -auto-approve
          fi

      - name: Get Terraform Outputs
        id: terraform-output
        working-directory: terraform
        run: |
          echo "vm_ip=$(terraform output -raw public_ip_address)" >> $GITHUB_OUTPUT
          echo "ssh_private_key<<EOF" >> $GITHUB_OUTPUT
          terraform output -raw ssh_private_key >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

  deploy:
    needs: terraform-apply
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          VM_IP="${{ needs.terraform-apply.outputs.vm_ip }}"
          SSH_KEY="${{ needs.terraform-apply.outputs.ssh_private_key }}"

          if [ -z "$VM_IP" ]; then
            echo "❌ VM IP is empty. Terraform apply may have failed."
            exit 1
          fi

          if [ -z "$SSH_KEY" ]; then
            echo "❌ SSH private key is empty. Terraform apply may have failed."
            exit 1
          fi

          echo "✅ VM IP: $VM_IP"
          echo "✅ SSH key length: $(echo "$SSH_KEY" | wc -c) characters"

          mkdir -p ~/.ssh
          echo "$SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          # Wait for VM to be ready and scan SSH keys with retry
          echo "Waiting for VM to be ready..."
          for i in {1..10}; do
            if ssh-keyscan -H "$VM_IP" >> ~/.ssh/known_hosts 2>/dev/null; then
              echo "✅ SSH keyscan successful on attempt $i"
              break
            else
              echo "⏳ SSH keyscan failed, retrying in 30 seconds... (attempt $i/10)"
              if [ $i -eq 10 ]; then
                echo "❌ SSH keyscan failed after 10 attempts"
                exit 1
              fi
              sleep 30
            fi
          done

      - name: Create deployment environment file
        run: |
          # Get database connection details from Terraform outputs
          DB_HOST=$(cd terraform && terraform output -raw database_server_fqdn)
          DB_PASSWORD=$(cd terraform && terraform output -raw database_admin_password)
          DB_USER=$(cd terraform && terraform output -raw database_admin_username)
          DB_NAME=$(cd terraform && terraform output -raw database_name)

          cat > .env.prod << EOF
          CONTAINER_IMAGE=ghcr.io/${{ github.repository }}:${{ github.sha }}
          SECRET_KEY=${{ secrets.TF_VAR_FLASK_SECRET_KEY }}
          DB_HOST=$DB_HOST
          DB_PORT=5432
          DB_NAME=$DB_NAME
          DB_USER=$DB_USER
          DB_PASSWORD=$DB_PASSWORD
          EOF

      - name: Copy deployment files to VM
        run: |
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no .env.prod azureuser@${{ needs.terraform-apply.outputs.vm_ip }}:/tmp/
          scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no scripts/deploy.sh azureuser@${{ needs.terraform-apply.outputs.vm_ip }}:/tmp/

      - name: Deploy application
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no azureuser@${{ needs.terraform-apply.outputs.vm_ip }} << 'EOF'
            source /tmp/.env.prod
            echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin
            sudo docker pull $CONTAINER_IMAGE
            sudo docker stop habit-tracker-app || true
            sudo docker rm habit-tracker-app || true
            sudo docker run -d \
              --name habit-tracker-app \
              --restart unless-stopped \
              -p 5000:5000 \
              -e FLASK_ENV=production \
              -e SECRET_KEY="$SECRET_KEY" \
              -e DB_HOST="$DB_HOST" \
              -e DB_PORT="$DB_PORT" \
              -e DB_NAME="$DB_NAME" \
              -e DB_USER="$DB_USER" \
              -e DB_PASSWORD="$DB_PASSWORD" \
              $CONTAINER_IMAGE
            sleep 30
            curl -f http://localhost:5000/ || exit 1
            echo "Application deployed successfully!"
          EOF

      - name: Setup SSL Certificate (if domain configured)
        run: |
          if [ -z "${{ secrets.TF_VAR_DOMAIN_NAME }}" ]; then
            echo "TF_VAR_DOMAIN_NAME secret is not set. Skipping SSL setup."
            exit 0
          fi
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no azureuser@${{ needs.terraform-apply.outputs.vm_ip }} << 'EOF'
            sudo sed -i "s/server_name _;/server_name ${{ secrets.TF_VAR_DOMAIN_NAME }};/g" /etc/nginx/sites-available/habit-tracker
            sudo nginx -t
            sudo systemctl reload nginx
            sudo certbot --nginx -d ${{ secrets.TF_VAR_DOMAIN_NAME }} --email ${{ secrets.TF_VAR_SSL_EMAIL }} --agree-tos --non-interactive
            echo "SSL certificate configured successfully!"
          EOF

      - name: Verify deployment
        run: |
          VM_IP=${{ needs.terraform-apply.outputs.vm_ip }}
          echo "Testing HTTP redirect..."
          curl -I http://$VM_IP | grep -q "301\|302" || echo "Warning: HTTP redirect not working"
          echo "Testing application health..."
          curl -f http://$VM_IP/ || curl -f https://$VM_IP/ || {
            echo "Application health check failed"
            exit 1
          }
          echo "Deployment verification completed successfully!"
          echo "Application is available at: http://$VM_IP"
          if [ -n "${{ secrets.TF_VAR_DOMAIN_NAME }}" ]; then
            echo "Domain URL: https://${{ secrets.TF_VAR_DOMAIN_NAME }}"
          fi

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          rm -f .env.prod
