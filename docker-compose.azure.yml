# =============================================================================
# Docker Compose Configuration for Azure Production Deployment
# =============================================================================
# This file is used for production deployment on Azure VM
# Environment variables should be set via .env file or CI/CD pipeline

version: '3.8'

services:
  habit-tracker:
    image: ${CONTAINER_IMAGE:-ghcr.io/your-org/habit-tracker:latest}
    container_name: habit-tracker-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      # Flask Configuration
      FLASK_ENV: production
      FLASK_APP: run.py
      SECRET_KEY: ${SECRET_KEY}
      
      # Database Configuration
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      
      # Application Configuration
      PYTHONPATH: /app/habit-tracker
      PYTHONDONTWRITEBYTECODE: 1
      PYTHONUNBUFFERED: 1
    
    volumes:
      # Persistent data storage
      - app_data:/app/instance
      - app_logs:/app/logs
      
    networks:
      - habit-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Optional: Add a monitoring container
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_REVIVE_STOPPED=false
    command: habit-tracker-app
    profiles:
      - monitoring

volumes:
  app_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/habit-tracker/data
  
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/habit-tracker

networks:
  habit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
