services:
  habit-tracker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: habit-tracker-app-prod
    ports:
      - '5000:5000'
    environment:
      FLASK_APP: run.py
      FLASK_ENV: production
      SECRET_KEY: ${SECRET_KEY:-change-this-in-production}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: habit_tracker
      DB_USER: habit_user
      DB_PASSWORD: habit_password
    working_dir: /app/habit-tracker
    networks:
      - habit-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    command: >
      sh -c "python init_db.py && gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 --access-logfile - --error-logfile - run:app"
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5000/']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: habit-tracker-db-prod
    environment:
      POSTGRES_DB: habit_tracker
      POSTGRES_USER: habit_user
      POSTGRES_PASSWORD: habit_password
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    networks:
      - habit-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U habit_user -d habit_tracker']
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data_prod:

networks:
  habit-network:
    driver: bridge
