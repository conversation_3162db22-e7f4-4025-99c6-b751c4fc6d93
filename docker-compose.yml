services:
  habit-tracker:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: habit-tracker-app
    ports:
      - '5000:5000'
    environment:
      FLASK_APP: run.py
      FLASK_ENV: development
      FLASK_DEBUG: 1
      SECRET_KEY: dev-secret-key-change-in-production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: habit_tracker
      DB_USER: habit_user
      DB_PASSWORD: habit_password
    volumes:
      - ./habit-tracker:/app/habit-tracker:ro
      - ./habit-tracker/requirements.txt:/app/requirements.txt:ro
    networks:
      - habit-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    command: >
      sh -c "cd habit-tracker && python init_db.py && flask run --host=0.0.0.0 --port=5000 --debug"
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5000/']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: habit-tracker-db
    environment:
      POSTGRES_DB: habit_tracker
      POSTGRES_USER: habit_user
      POSTGRES_PASSWORD: habit_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - habit-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U habit_user -d habit_tracker']
      interval: 10s
      timeout: 5s
      retries: 5

  pg-admin:
    image: dpage/pgadmin4
    container_name: pg-admin
    ports:
      - '8080:80'
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - habit-network

volumes:
  postgres_data:

networks:
  habit-network:
    driver: bridge
