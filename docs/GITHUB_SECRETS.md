# GitHub Secrets Configuration for Habit Tracker

This document outlines all the GitHub secrets required for the CI/CD pipeline to deploy the Habit Tracker application to Azure.

## Required GitHub Secrets

### Azure Authentication

#### `AZURE_CREDENTIALS`
**Required**: Yes  
**Type**: JSON  
**Description**: Azure service principal credentials for Terraform and Azure CLI authentication.

**How to create**:
1. Create a service principal in Azure:
   ```bash
   az ad sp create-for-rbac --name "habit-tracker-github-actions" \
     --role contributor \
     --scopes /subscriptions/{subscription-id} \
     --sdk-auth
   ```

2. The output should look like this:
   ```json
   {
     "clientId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
     "clientSecret": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
     "subscriptionId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
     "tenantId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
   }
   ```

3. Copy the entire JSON output and paste it as the value for `AZURE_CREDENTIALS`

### Application Configuration

#### `FLASK_SECRET_KEY`
**Required**: Yes  
**Type**: String  
**Description**: Secret key for Flask session management and CSRF protection.

**How to generate**:
```bash
python -c "import secrets; print(secrets.token_hex(32))"
```

**Example**: `a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456`

### Database Configuration

#### `DB_HOST`
**Required**: Yes (set by Terraform)  
**Type**: String  
**Description**: PostgreSQL server hostname (automatically set by Terraform output).

**Note**: This will be automatically populated by the Terraform deployment. You can also set it manually if using an external database.

#### `DB_NAME`
**Required**: Yes  
**Type**: String  
**Description**: PostgreSQL database name.

**Default**: `habit_tracker`

#### `DB_USER`
**Required**: Yes  
**Type**: String  
**Description**: PostgreSQL database username.

**Default**: `habitadmin` (or as configured in Terraform)

#### `DB_PASSWORD`
**Required**: Yes (set by Terraform)  
**Type**: String  
**Description**: PostgreSQL database password (automatically generated by Terraform).

**Note**: This will be automatically populated by the Terraform deployment.

### SSL/Domain Configuration (Optional)

#### `DOMAIN_NAME`
**Required**: No  
**Type**: String  
**Description**: Custom domain name for the application (e.g., `myapp.example.com`).

**Example**: `habit-tracker.yourdomain.com`

**Note**: If not provided, the application will be accessible via the Azure VM's public IP address.

#### `SSL_EMAIL`
**Required**: Only if `DOMAIN_NAME` is set  
**Type**: String  
**Description**: Email address for Let's Encrypt SSL certificate registration.

**Example**: `<EMAIL>`

### Container Registry (Automatically configured)

#### `GITHUB_TOKEN`
**Required**: Yes (automatically available)  
**Type**: String  
**Description**: GitHub token for accessing GitHub Container Registry.

**Note**: This is automatically provided by GitHub Actions as `secrets.GITHUB_TOKEN`. No manual configuration needed.

## Setting Up GitHub Secrets

### Via GitHub Web Interface

1. Go to your repository on GitHub
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret**
5. Enter the secret name and value
6. Click **Add secret**

### Via GitHub CLI

```bash
# Set Azure credentials
gh secret set AZURE_CREDENTIALS --body "$(cat azure-credentials.json)"

# Set Flask secret key
gh secret set FLASK_SECRET_KEY --body "$(python -c 'import secrets; print(secrets.token_hex(32))')"

# Set database configuration
gh secret set DB_NAME --body "habit_tracker"
gh secret set DB_USER --body "habitadmin"

# Set domain configuration (optional)
gh secret set DOMAIN_NAME --body "your-domain.com"
gh secret set SSL_EMAIL --body "<EMAIL>"
```

## Environment-Specific Secrets

### Production Environment

For production deployment, ensure all secrets are set with production values:

- `FLASK_SECRET_KEY`: Strong, unique secret key
- `DOMAIN_NAME`: Production domain name
- `SSL_EMAIL`: Valid email for SSL certificate notifications

### Development/Staging Environment

You can create environment-specific secrets by using different secret names or GitHub environments:

- Create a `development` environment in GitHub
- Set environment-specific secrets in that environment
- Modify the workflow to use different environments

## Security Best Practices

### Secret Management

1. **Rotate secrets regularly**: Change secrets periodically, especially after team member changes
2. **Use strong secrets**: Generate cryptographically secure random values
3. **Limit access**: Only grant access to secrets to necessary team members
4. **Monitor usage**: Review secret usage in GitHub Actions logs
5. **Use environments**: Separate production and development secrets using GitHub environments

### Azure Service Principal

1. **Principle of least privilege**: Grant only necessary permissions
2. **Scope limitations**: Limit the service principal scope to specific resource groups
3. **Regular rotation**: Rotate client secrets regularly
4. **Monitor activity**: Review Azure AD sign-in logs for the service principal

## Troubleshooting

### Common Issues

#### Azure Authentication Fails
- Verify `AZURE_CREDENTIALS` JSON format is correct
- Check service principal permissions in Azure
- Ensure subscription ID is correct

#### Database Connection Fails
- Verify database credentials are correct
- Check if Terraform has completed successfully
- Ensure database server is accessible from the VM

#### SSL Certificate Issues
- Verify domain DNS points to the correct IP address
- Check email address format for Let's Encrypt
- Ensure domain name is correctly formatted

### Validation Commands

Test your secrets configuration:

```bash
# Test Azure authentication
az login --service-principal \
  --username $CLIENT_ID \
  --password $CLIENT_SECRET \
  --tenant $TENANT_ID

# Test database connection
psql "************************************************/$DB_NAME"

# Test domain resolution
nslookup $DOMAIN_NAME
```

## Required Secrets Summary

| Secret Name | Required | Auto-Generated | Description |
|-------------|----------|----------------|-------------|
| `AZURE_CREDENTIALS` | ✅ | ❌ | Azure service principal credentials |
| `FLASK_SECRET_KEY` | ✅ | ❌ | Flask application secret key |
| `DB_HOST` | ✅ | ✅ | Database server hostname |
| `DB_NAME` | ✅ | ❌ | Database name |
| `DB_USER` | ✅ | ❌ | Database username |
| `DB_PASSWORD` | ✅ | ✅ | Database password |
| `DOMAIN_NAME` | ❌ | ❌ | Custom domain (optional) |
| `SSL_EMAIL` | ❌ | ❌ | Email for SSL certificates |
| `GITHUB_TOKEN` | ✅ | ✅ | GitHub registry access |

## Next Steps

1. Set up the required secrets in your GitHub repository
2. Configure Azure service principal with appropriate permissions
3. Run the GitHub Actions workflow to deploy your application
4. Monitor the deployment logs for any issues

For additional help, refer to the main [README.md](../README.md) and [Terraform documentation](../terraform/README.md).
