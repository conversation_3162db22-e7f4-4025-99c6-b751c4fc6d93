#cloud-config
# =============================================================================
# Cloud-init configuration for Habit Tracker VM
# =============================================================================

package_update: true
package_upgrade: true

packages:
  - apt-transport-https
  - ca-certificates
  - curl
  - gnupg
  - lsb-release
  - software-properties-common
  - nginx
  - certbot
  - python3-certbot-nginx
  - ufw
  - fail2ban
  - unattended-upgrades
  - htop
  - git
  - jq

# Create application user
users:
  - name: appuser
    groups: docker
    shell: /bin/bash
    sudo: ["ALL=(ALL) NOPASSWD:ALL"]

write_files:
  # Docker daemon configuration
  - path: /etc/docker/daemon.json
    content: |
      {
        "log-driver": "json-file",
        "log-opts": {
          "max-size": "10m",
          "max-file": "3"
        },
        "storage-driver": "overlay2"
      }
    permissions: "0644"

  # Nginx configuration template
  - path: /etc/nginx/sites-available/habit-tracker
    content: |
      server {
          listen 80;
          server_name _;
          
          # Redirect HTTP to HTTPS
          return 301 https://$server_name$request_uri;
      }

      server {
          listen 443 ssl http2;
          server_name _;
          
          # SSL configuration will be added by certbot
          
          # Security headers
          add_header X-Frame-Options DENY;
          add_header X-Content-Type-Options nosniff;
          add_header X-XSS-Protection "1; mode=block";
          add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
          
          # Proxy to application
          location / {
              proxy_pass http://localhost:5000;
              proxy_set_header Host $host;
              proxy_set_header X-Real-IP $remote_addr;
              proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Proto $scheme;
              proxy_connect_timeout 60s;
              proxy_send_timeout 60s;
              proxy_read_timeout 60s;
          }
          
          # Health check endpoint
          location /health {
              access_log off;
              return 200 "healthy\n";
              add_header Content-Type text/plain;
          }
      }
    permissions: "0644"

  # UFW configuration
  - path: /etc/ufw/applications.d/habit-tracker
    content: |
      [Habit Tracker]
      title=Habit Tracker Application
      description=Flask application with Nginx reverse proxy
      ports=80,443/tcp
    permissions: "0644"

  # Fail2ban jail configuration
  - path: /etc/fail2ban/jail.d/habit-tracker.conf
    content: |
      [sshd]
      enabled = true
      port = ssh
      filter = sshd
      logpath = /var/log/auth.log
      maxretry = 3
      bantime = 3600

      [nginx-http-auth]
      enabled = true
      filter = nginx-http-auth
      logpath = /var/log/nginx/error.log
      maxretry = 5
      bantime = 3600
    permissions: "0644"

  # Application deployment script
  - path: /home/<USER>/deploy-app.sh
    content: |
      #!/bin/bash
      set -e

      echo "Starting application deployment..."

      # Create application directory
      sudo mkdir -p /opt/habit-tracker
      sudo chown appuser:appuser /opt/habit-tracker

      # Create docker-compose.yml for production
      cat > /opt/habit-tracker/docker-compose.yml << 'EOF'
      version: '3.8'

      services:
        habit-tracker:
          image: ${CONTAINER_IMAGE}
          container_name: habit-tracker-app
          restart: unless-stopped
          ports:
            - "5000:5000"
          environment:
            FLASK_ENV: production
            SECRET_KEY: ${SECRET_KEY}
            # Note: Database configuration handled by Docker Compose
          healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 40s
          logging:
            driver: "json-file"
            options:
              max-size: "10m"
              max-file: "3"
      EOF

      echo "Application deployment script created successfully"
    permissions: "0755"
    owner: ${admin_username}:${admin_username}

runcmd:
  # Update system first
  - apt-get update
  - apt-get upgrade -y

  # Install Docker
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
  - echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
  - apt-get update
  - apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

  # Start and enable Docker
  - systemctl start docker
  - systemctl enable docker

  # Add users to docker group
  - usermod -aG docker ${admin_username}
  - usermod -aG docker appuser

  # Configure Nginx
  - rm -f /etc/nginx/sites-enabled/default
  - ln -sf /etc/nginx/sites-available/habit-tracker /etc/nginx/sites-enabled/
  - nginx -t
  - systemctl restart nginx
  - systemctl enable nginx

  # Configure UFW firewall
  - ufw --force enable
  - ufw default deny incoming
  - ufw default allow outgoing
  - ufw allow ssh
  - ufw allow 'Nginx Full'
  - ufw allow 'Habit Tracker'

  # Start fail2ban
  - systemctl start fail2ban
  - systemctl enable fail2ban

  # Configure automatic security updates
  - echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades
  - systemctl enable unattended-upgrades

  # Create log directories
  - mkdir -p /var/log/habit-tracker
  - chown appuser:appuser /var/log/habit-tracker

  # Create readiness indicator file
  - echo "VM initialization completed at $(date)" > /tmp/vm-ready
  - chmod 644 /tmp/vm-ready

  # Ensure SSH service is running and ready
  - systemctl restart ssh
  - systemctl enable ssh

  # Set up log rotation
  - |
    cat > /etc/logrotate.d/habit-tracker << 'EOF'
    /var/log/habit-tracker/*.log {
        daily
        missingok
        rotate 14
        compress
        delaycompress
        notifempty
        create 644 appuser appuser
    }
    EOF

final_message: "Habit Tracker VM setup completed successfully!"
